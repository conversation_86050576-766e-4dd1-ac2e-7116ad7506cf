## 获取服务套餐详情

**接口地址**:`/publicbiz/employment/service-package/{id}`

**请求方式**:`GET`

**请求数据类型**:`application/x-www-form-urlencoded`

**响应数据类型**:`*/*`

**接口描述**:

**请求参数**:

**请求参数**:

| 参数名称 | 参数说明 | 请求类型 | 是否必须 | 数据类型       | schema |
| -------- | -------- | -------- | -------- | -------------- | ------ |
| id       |          | path     | true     | integer(int64) |        |

**响应状态**:

| 状态码 | 说明 | schema                           |
| ------ | ---- | -------------------------------- |
| 200    | OK   | CommonResultServicePackageRespVO |

**响应参数**:

| 参数名称 | 参数说明 | 类型 | schema |
| --- | --- | --- | --- |
| code |  | integer(int32) | integer(int32) |
| data |  | ServicePackageRespVO | ServicePackageRespVO |
| &emsp;&emsp;id | 套餐ID | integer(int64) |  |
| &emsp;&emsp;name | 套餐名称 | string |  |
| &emsp;&emsp;category | 服务分类 | string |  |
| &emsp;&emsp;thumbnail | 套餐主图URL | string |  |
| &emsp;&emsp;price | 套餐价格 | number |  |
| &emsp;&emsp;originalPrice | 原价 | number |  |
| &emsp;&emsp;unit | 价格单位 | string |  |
| &emsp;&emsp;serviceDuration | 服务时长 | string |  |
| &emsp;&emsp;packageType | 套餐类型 | string |  |
| &emsp;&emsp;taskSplitRule | 任务拆分规则 | string |  |
| &emsp;&emsp;serviceDescription | 服务描述 | string |  |
| &emsp;&emsp;serviceDetails | 详细服务内容 | string |  |
| &emsp;&emsp;serviceProcess | 服务流程 | string |  |
| &emsp;&emsp;purchaseNotice | 购买须知 | string |  |
| &emsp;&emsp;status | 状态 | string |  |
| &emsp;&emsp;advanceBookingDays | 预约时间范围 | integer(int32) |  |
| &emsp;&emsp;timeSelectionMode | 时间选择模式 | string |  |
| &emsp;&emsp;appointmentMode | 预约模式 | string |  |
| &emsp;&emsp;serviceStartTime | 服务开始时间 | string |  |
| &emsp;&emsp;addressSetting | 地址设置 | string |  |
| &emsp;&emsp;maxBookingDays | 最大预约天数 | integer(int32) |  |
| &emsp;&emsp;cancellationPolicy | 取消政策 | string |  |
| &emsp;&emsp;carouselList | 轮播图列表 | array | ServicePackageCarouselRespVO |
| &emsp;&emsp;&emsp;&emsp;id | 轮播图ID | integer(int64) |  |
| &emsp;&emsp;&emsp;&emsp;packageId | 套餐ID | integer(int64) |  |
| &emsp;&emsp;&emsp;&emsp;imageUrl | 轮播图URL | string |  |
| &emsp;&emsp;&emsp;&emsp;sortOrder | 排序 | integer(int32) |  |
| &emsp;&emsp;&emsp;&emsp;status | 状态 | integer(int32) |  |
| &emsp;&emsp;&emsp;&emsp;createTime | 创建时间 | string(date-time) |  |
| &emsp;&emsp;&emsp;&emsp;updateTime | 更新时间 | string(date-time) |  |
| &emsp;&emsp;featureList | 特色标签列表 | array | ServicePackageFeatureRespVO |
| &emsp;&emsp;&emsp;&emsp;id | 特色标签ID | integer(int64) |  |
| &emsp;&emsp;&emsp;&emsp;packageId | 套餐ID | integer(int64) |  |
| &emsp;&emsp;&emsp;&emsp;featureName | 特色标签名称 | string |  |
| &emsp;&emsp;&emsp;&emsp;sortOrder | 排序 | integer(int32) |  |
| &emsp;&emsp;&emsp;&emsp;createTime | 创建时间 | string(date-time) |  |
| &emsp;&emsp;&emsp;&emsp;updateTime | 更新时间 | string(date-time) |  |
| &emsp;&emsp;createTime | 创建时间 | string(date-time) |  |
| &emsp;&emsp;updateTime | 更新时间 | string(date-time) |  |
| msg |  | string |  |

**响应示例**:

```javascript
{
	"code": 0,
	"data": {
		"id": 0,
		"name": "",
		"category": "",
		"thumbnail": "",
		"price": 0,
		"originalPrice": 0,
		"unit": "",
		"serviceDuration": "",
		"packageType": "",
		"taskSplitRule": "",
		"serviceDescription": "",
		"serviceDetails": "",
		"serviceProcess": "",
		"purchaseNotice": "",
		"status": "",
		"advanceBookingDays": 0,
		"timeSelectionMode": "",
		"appointmentMode": "",
		"serviceStartTime": "",
		"addressSetting": "",
		"maxBookingDays": 0,
		"cancellationPolicy": "",
		"carouselList": [
			{
				"id": 0,
				"packageId": 0,
				"imageUrl": "",
				"sortOrder": 0,
				"status": 0,
				"createTime": "",
				"updateTime": ""
			}
		],
		"featureList": [
			{
				"id": 0,
				"packageId": 0,
				"featureName": "",
				"sortOrder": 0,
				"createTime": "",
				"updateTime": ""
			}
		],
		"createTime": "",
		"updateTime": ""
	},
	"msg": ""
}
```

## 删除服务套餐（软删除）

**接口地址**:`/admin-api/publicbiz/employment/service-package/{id}`

**请求方式**:`DELETE`

**请求数据类型**:`application/x-www-form-urlencoded`

**响应数据类型**:`*/*`

**接口描述**:

**请求参数**:

**请求参数**:

| 参数名称 | 参数说明 | 请求类型 | 是否必须 | 数据类型       | schema |
| -------- | -------- | -------- | -------- | -------------- | ------ |
| id       |          | path     | true     | integer(int64) |        |

**响应状态**:

| 状态码 | 说明 | schema              |
| ------ | ---- | ------------------- |
| 200    | OK   | CommonResultBoolean |

**响应参数**:

| 参数名称 | 参数说明 | 类型           | schema         |
| -------- | -------- | -------------- | -------------- |
| code     |          | integer(int32) | integer(int32) |
| data     |          | boolean        |                |
| msg      |          | string         |                |

**响应示例**:

```javascript
{
	"code": 0,
	"data": true,
	"msg": ""
}
```

## 新增服务套餐

**接口地址**:`/publicbiz/employment/service-package/create`

**请求方式**:`POST`

**请求数据类型**:`application/x-www-form-urlencoded,application/json`

**响应数据类型**:`*/*`

**接口描述**:

**请求示例**:

```javascript
{
  "name": "",
  "category": "",
  "thumbnail": "",
  "price": 0,
  "originalPrice": 0,
  "unit": "",
  "serviceDuration": "",
  "packageType": "",
  "taskSplitRule": "",
  "serviceDescription": "",
  "serviceDetails": "",
  "serviceProcess": "",
  "purchaseNotice": "",
  "status": "",
  "advanceBookingDays": 0,
  "timeSelectionMode": "",
  "appointmentMode": "",
  "serviceStartTime": "",
  "addressSetting": "",
  "maxBookingDays": 0,
  "cancellationPolicy": "",
  "carouselList": [
    {
      "id": 0,
      "imageUrl": "",
      "sortOrder": 0,
      "status": 0
    }
  ],
  "featureList": [
    {
      "id": 0,
      "featureName": "",
      "sortOrder": 0
    }
  ]
}
```

**请求参数**:

**请求参数**:

| 参数名称 | 参数说明 | 请求类型 | 是否必须 | 数据类型 | schema |
| --- | --- | --- | --- | --- | --- |
| servicePackageSaveReqVO | 就业服务-服务套餐新增 Request VO | body | true | ServicePackageSaveReqVO | ServicePackageSaveReqVO |
| &emsp;&emsp;name | 套餐名称 |  | true | string |  |
| &emsp;&emsp;category | 服务分类 |  | true | string |  |
| &emsp;&emsp;thumbnail | 套餐主图URL |  | false | string |  |
| &emsp;&emsp;price | 套餐价格 |  | true | number |  |
| &emsp;&emsp;originalPrice | 原价 |  | false | number |  |
| &emsp;&emsp;unit | 价格单位 |  | true | string |  |
| &emsp;&emsp;serviceDuration | 服务时长 |  | false | string |  |
| &emsp;&emsp;packageType | 套餐类型 |  | true | string |  |
| &emsp;&emsp;taskSplitRule | 任务拆分规则 |  | false | string |  |
| &emsp;&emsp;serviceDescription | 服务描述 |  | false | string |  |
| &emsp;&emsp;serviceDetails | 详细服务内容 |  | false | string |  |
| &emsp;&emsp;serviceProcess | 服务流程 |  | false | string |  |
| &emsp;&emsp;purchaseNotice | 购买须知 |  | false | string |  |
| &emsp;&emsp;status | 状态 |  | false | string |  |
| &emsp;&emsp;advanceBookingDays | 预约时间范围 |  | false | integer(int32) |  |
| &emsp;&emsp;timeSelectionMode | 时间选择模式 |  | false | string |  |
| &emsp;&emsp;appointmentMode | 预约模式 |  | false | string |  |
| &emsp;&emsp;serviceStartTime | 服务开始时间 |  | false | string |  |
| &emsp;&emsp;addressSetting | 地址设置 |  | false | string |  |
| &emsp;&emsp;maxBookingDays | 最大预约天数 |  | false | integer(int32) |  |
| &emsp;&emsp;cancellationPolicy | 取消政策 |  | false | string |  |
| &emsp;&emsp;carouselList | 轮播图列表 |  | false | array | ServicePackageCarouselSaveReqVO |
| &emsp;&emsp;&emsp;&emsp;id | 轮播图ID（更新时使用） |  | false | integer(int64) |  |
| &emsp;&emsp;&emsp;&emsp;imageUrl | 轮播图URL |  | true | string |  |
| &emsp;&emsp;&emsp;&emsp;sortOrder | 排序 |  | false | integer(int32) |  |
| &emsp;&emsp;&emsp;&emsp;status | 状态 |  | false | integer(int32) |  |
| &emsp;&emsp;featureList | 特色标签列表 |  | false | array | ServicePackageFeatureSaveReqVO |
| &emsp;&emsp;&emsp;&emsp;id | 特色标签ID（更新时使用） |  | false | integer(int64) |  |
| &emsp;&emsp;&emsp;&emsp;featureName | 特色标签名称 |  | true | string |  |
| &emsp;&emsp;&emsp;&emsp;sortOrder | 排序 |  | false | integer(int32) |  |

**响应状态**:

| 状态码 | 说明 | schema           |
| ------ | ---- | ---------------- |
| 200    | OK   | CommonResultLong |

**响应参数**:

| 参数名称 | 参数说明 | 类型           | schema         |
| -------- | -------- | -------------- | -------------- |
| code     |          | integer(int32) | integer(int32) |
| data     |          | integer(int64) | integer(int64) |
| msg      |          | string         |                |

**响应示例**:

```javascript
{
	"code": 0,
	"data": 0,
	"msg": ""
}
```

## 获取服务套餐分页

**接口地址**:`/publicbiz/employment/service-package/page`

**请求方式**:`GET`

**请求数据类型**:`application/x-www-form-urlencoded`

**响应数据类型**:`*/*`

**接口描述**:

**请求参数**:

**请求参数**:

| 参数名称    | 参数说明               | 请求类型 | 是否必须 | 数据类型 | schema |
| ----------- | ---------------------- | -------- | -------- | -------- | ------ |
| pageNo      | 页码，从 1 开始        | query    | true     | string   |        |
| pageSize    | 每页条数，最大值为 100 | query    | true     | string   |        |
| keyword     | 套餐名称或ID关键词     | query    | false    | string   |        |
| category    | 服务分类               | query    | false    | string   |        |
| status      | 状态                   | query    | false    | string   |        |
| packageType | 套餐类型               | query    | false    | string   |        |

**响应状态**:

| 状态码 | 说明 | schema                                     |
| ------ | ---- | ------------------------------------------ |
| 200    | OK   | CommonResultPageResultServicePackageRespVO |

**响应参数**:

| 参数名称 | 参数说明 | 类型 | schema |
| --- | --- | --- | --- |
| code |  | integer(int32) | integer(int32) |
| data |  | PageResultServicePackageRespVO | PageResultServicePackageRespVO |
| &emsp;&emsp;list | 数据 | array | ServicePackageRespVO |
| &emsp;&emsp;&emsp;&emsp;id | 套餐ID | integer(int64) |  |
| &emsp;&emsp;&emsp;&emsp;name | 套餐名称 | string |  |
| &emsp;&emsp;&emsp;&emsp;category | 服务分类 | string |  |
| &emsp;&emsp;&emsp;&emsp;thumbnail | 套餐主图URL | string |  |
| &emsp;&emsp;&emsp;&emsp;price | 套餐价格 | number |  |
| &emsp;&emsp;&emsp;&emsp;originalPrice | 原价 | number |  |
| &emsp;&emsp;&emsp;&emsp;unit | 价格单位 | string |  |
| &emsp;&emsp;&emsp;&emsp;serviceDuration | 服务时长 | string |  |
| &emsp;&emsp;&emsp;&emsp;packageType | 套餐类型 | string |  |
| &emsp;&emsp;&emsp;&emsp;taskSplitRule | 任务拆分规则 | string |  |
| &emsp;&emsp;&emsp;&emsp;serviceDescription | 服务描述 | string |  |
| &emsp;&emsp;&emsp;&emsp;serviceDetails | 详细服务内容 | string |  |
| &emsp;&emsp;&emsp;&emsp;serviceProcess | 服务流程 | string |  |
| &emsp;&emsp;&emsp;&emsp;purchaseNotice | 购买须知 | string |  |
| &emsp;&emsp;&emsp;&emsp;status | 状态 | string |  |
| &emsp;&emsp;&emsp;&emsp;advanceBookingDays | 预约时间范围 | integer(int32) |  |
| &emsp;&emsp;&emsp;&emsp;timeSelectionMode | 时间选择模式 | string |  |
| &emsp;&emsp;&emsp;&emsp;appointmentMode | 预约模式 | string |  |
| &emsp;&emsp;&emsp;&emsp;serviceStartTime | 服务开始时间 | string |  |
| &emsp;&emsp;&emsp;&emsp;addressSetting | 地址设置 | string |  |
| &emsp;&emsp;&emsp;&emsp;maxBookingDays | 最大预约天数 | integer(int32) |  |
| &emsp;&emsp;&emsp;&emsp;cancellationPolicy | 取消政策 | string |  |
| &emsp;&emsp;&emsp;&emsp;carouselList | 轮播图列表 | array | ServicePackageCarouselRespVO |
| &emsp;&emsp;&emsp;&emsp;&emsp;&emsp;id | 轮播图ID | integer(int64) |  |
| &emsp;&emsp;&emsp;&emsp;&emsp;&emsp;packageId | 套餐ID | integer(int64) |  |
| &emsp;&emsp;&emsp;&emsp;&emsp;&emsp;imageUrl | 轮播图URL | string |  |
| &emsp;&emsp;&emsp;&emsp;&emsp;&emsp;sortOrder | 排序 | integer(int32) |  |
| &emsp;&emsp;&emsp;&emsp;&emsp;&emsp;status | 状态 | integer(int32) |  |
| &emsp;&emsp;&emsp;&emsp;&emsp;&emsp;createTime | 创建时间 | string(date-time) |  |
| &emsp;&emsp;&emsp;&emsp;&emsp;&emsp;updateTime | 更新时间 | string(date-time) |  |
| &emsp;&emsp;&emsp;&emsp;featureList | 特色标签列表 | array | ServicePackageFeatureRespVO |
| &emsp;&emsp;&emsp;&emsp;&emsp;&emsp;id | 特色标签ID | integer(int64) |  |
| &emsp;&emsp;&emsp;&emsp;&emsp;&emsp;packageId | 套餐ID | integer(int64) |  |
| &emsp;&emsp;&emsp;&emsp;&emsp;&emsp;featureName | 特色标签名称 | string |  |
| &emsp;&emsp;&emsp;&emsp;&emsp;&emsp;sortOrder | 排序 | integer(int32) |  |
| &emsp;&emsp;&emsp;&emsp;&emsp;&emsp;createTime | 创建时间 | string(date-time) |  |
| &emsp;&emsp;&emsp;&emsp;&emsp;&emsp;updateTime | 更新时间 | string(date-time) |  |
| &emsp;&emsp;&emsp;&emsp;createTime | 创建时间 | string(date-time) |  |
| &emsp;&emsp;&emsp;&emsp;updateTime | 更新时间 | string(date-time) |  |
| &emsp;&emsp;total | 总量 | integer(int64) |  |
| msg |  | string |  |

**响应示例**:

```javascript
{
	"code": 0,
	"data": {
		"list": [
			{
				"id": 0,
				"name": "",
				"category": "",
				"thumbnail": "",
				"price": 0,
				"originalPrice": 0,
				"unit": "",
				"serviceDuration": "",
				"packageType": "",
				"taskSplitRule": "",
				"serviceDescription": "",
				"serviceDetails": "",
				"serviceProcess": "",
				"purchaseNotice": "",
				"status": "",
				"advanceBookingDays": 0,
				"timeSelectionMode": "",
				"appointmentMode": "",
				"serviceStartTime": "",
				"addressSetting": "",
				"maxBookingDays": 0,
				"cancellationPolicy": "",
				"carouselList": [
					{
						"id": 0,
						"packageId": 0,
						"imageUrl": "",
						"sortOrder": 0,
						"status": 0,
						"createTime": "",
						"updateTime": ""
					}
				],
				"featureList": [
					{
						"id": 0,
						"packageId": 0,
						"featureName": "",
						"sortOrder": 0,
						"createTime": "",
						"updateTime": ""
					}
				],
				"createTime": "",
				"updateTime": ""
			}
		],
		"total": 0
	},
	"msg": ""
}
```

## 批量更新服务套餐状态

**接口地址**:`/publicbiz/employment/service-package/status`

**请求方式**:`PUT`

**请求数据类型**:`application/x-www-form-urlencoded,application/json`

**响应数据类型**:`*/*`

**接口描述**:

**请求示例**:

```javascript
{
  "ids": [],
  "status": ""
}
```

**请求参数**:

**请求参数**:

| 参数名称 | 参数说明 | 请求类型 | 是否必须 | 数据类型 | schema |
| --- | --- | --- | --- | --- | --- |
| servicePackageStatusUpdateReqVO | 就业服务-服务套餐状态更新 Request VO | body | true | ServicePackageStatusUpdateReqVO | ServicePackageStatusUpdateReqVO |
| &emsp;&emsp;ids | 套餐ID列表 |  | true | array | integer(int64) |
| &emsp;&emsp;status | 目标状态 |  | true | string |  |

**响应状态**:

| 状态码 | 说明 | schema              |
| ------ | ---- | ------------------- |
| 200    | OK   | CommonResultBoolean |

**响应参数**:

| 参数名称 | 参数说明 | 类型           | schema         |
| -------- | -------- | -------------- | -------------- |
| code     |          | integer(int32) | integer(int32) |
| data     |          | boolean        |                |
| msg      |          | string         |                |

**响应示例**:

```javascript
{
	"code": 0,
	"data": true,
	"msg": ""
}
```

## 更新服务套餐

**接口地址**:`/publicbiz/employment/service-package/update`

**请求方式**:`PUT`

**请求数据类型**:`application/x-www-form-urlencoded,application/json`

**响应数据类型**:`*/*`

**接口描述**:

**请求示例**:

```javascript
{
  "id": 0,
  "name": "",
  "category": "",
  "thumbnail": "",
  "price": 0,
  "originalPrice": 0,
  "unit": "",
  "serviceDuration": "",
  "packageType": "",
  "taskSplitRule": "",
  "serviceDescription": "",
  "serviceDetails": "",
  "serviceProcess": "",
  "purchaseNotice": "",
  "status": "",
  "advanceBookingDays": 0,
  "timeSelectionMode": "",
  "appointmentMode": "",
  "serviceStartTime": "",
  "addressSetting": "",
  "maxBookingDays": 0,
  "cancellationPolicy": "",
  "carouselList": [
    {
      "id": 0,
      "imageUrl": "",
      "sortOrder": 0,
      "status": 0
    }
  ],
  "featureList": [
    {
      "id": 0,
      "featureName": "",
      "sortOrder": 0
    }
  ]
}
```

**请求参数**:

**请求参数**:

| 参数名称 | 参数说明 | 请求类型 | 是否必须 | 数据类型 | schema |
| --- | --- | --- | --- | --- | --- |
| servicePackageUpdateReqVO | 就业服务-服务套餐更新 Request VO | body | true | ServicePackageUpdateReqVO | ServicePackageUpdateReqVO |
| &emsp;&emsp;id | 套餐ID |  | true | integer(int64) |  |
| &emsp;&emsp;name | 套餐名称 |  | true | string |  |
| &emsp;&emsp;category | 服务分类 |  | true | string |  |
| &emsp;&emsp;thumbnail | 套餐主图URL |  | false | string |  |
| &emsp;&emsp;price | 套餐价格 |  | true | number |  |
| &emsp;&emsp;originalPrice | 原价 |  | false | number |  |
| &emsp;&emsp;unit | 价格单位 |  | true | string |  |
| &emsp;&emsp;serviceDuration | 服务时长 |  | false | string |  |
| &emsp;&emsp;packageType | 套餐类型 |  | true | string |  |
| &emsp;&emsp;taskSplitRule | 任务拆分规则 |  | false | string |  |
| &emsp;&emsp;serviceDescription | 服务描述 |  | false | string |  |
| &emsp;&emsp;serviceDetails | 详细服务内容 |  | false | string |  |
| &emsp;&emsp;serviceProcess | 服务流程 |  | false | string |  |
| &emsp;&emsp;purchaseNotice | 购买须知 |  | false | string |  |
| &emsp;&emsp;status | 状态 |  | false | string |  |
| &emsp;&emsp;advanceBookingDays | 预约时间范围 |  | false | integer(int32) |  |
| &emsp;&emsp;timeSelectionMode | 时间选择模式 |  | false | string |  |
| &emsp;&emsp;appointmentMode | 预约模式 |  | false | string |  |
| &emsp;&emsp;serviceStartTime | 服务开始时间 |  | false | string |  |
| &emsp;&emsp;addressSetting | 地址设置 |  | false | string |  |
| &emsp;&emsp;maxBookingDays | 最大预约天数 |  | false | integer(int32) |  |
| &emsp;&emsp;cancellationPolicy | 取消政策 |  | false | string |  |
| &emsp;&emsp;carouselList | 轮播图列表 |  | false | array | ServicePackageCarouselSaveReqVO |
| &emsp;&emsp;&emsp;&emsp;id | 轮播图ID（更新时使用） |  | false | integer(int64) |  |
| &emsp;&emsp;&emsp;&emsp;imageUrl | 轮播图URL |  | true | string |  |
| &emsp;&emsp;&emsp;&emsp;sortOrder | 排序 |  | false | integer(int32) |  |
| &emsp;&emsp;&emsp;&emsp;status | 状态 |  | false | integer(int32) |  |
| &emsp;&emsp;featureList | 特色标签列表 |  | false | array | ServicePackageFeatureSaveReqVO |
| &emsp;&emsp;&emsp;&emsp;id | 特色标签ID（更新时使用） |  | false | integer(int64) |  |
| &emsp;&emsp;&emsp;&emsp;featureName | 特色标签名称 |  | true | string |  |
| &emsp;&emsp;&emsp;&emsp;sortOrder | 排序 |  | false | integer(int32) |  |

**响应状态**:

| 状态码 | 说明 | schema              |
| ------ | ---- | ------------------- |
| 200    | OK   | CommonResultBoolean |

**响应参数**:

| 参数名称 | 参数说明 | 类型           | schema         |
| -------- | -------- | -------------- | -------------- |
| code     |          | integer(int32) | integer(int32) |
| data     |          | boolean        |                |
| msg      |          | string         |                |

**响应示例**:

```javascript
{
	"code": 0,
	"data": true,
	"msg": ""
}
```

## 上传文件

**接口地址**:`/infra/file/upload`

**请求方式**:`POST`

**请求数据类型**:`application/x-www-form-urlencoded`

**响应数据类型**:`*/*`

**接口描述**:<p>模式一：后端上传文件</p>

**请求参数**:

**请求参数**:

| 参数名称  | 参数说明 | 请求类型 | 是否必须 | 数据类型 | schema |
| --------- | -------- | -------- | -------- | -------- | ------ |
| directory | 文件目录 | query    | false    | string   |        |

**响应状态**:

| 状态码 | 说明 | schema             |
| ------ | ---- | ------------------ |
| 200    | OK   | CommonResultString |

**响应参数**:

| 参数名称 | 参数说明 | 类型           | schema         |
| -------- | -------- | -------------- | -------------- |
| code     |          | integer(int32) | integer(int32) |
| data     |          | string         |                |
| msg      |          | string         |                |

**响应示例**:

```javascript
{
	"code": 0,
	"data": "",
	"msg": ""
}
```

## 移动服务套餐到回收站

**接口地址**:`/publicbiz/employment/service-package/{id}/move-to-recycle`

**请求方式**:`DELETE`

**请求数据类型**:`application/x-www-form-urlencoded`

**响应数据类型**:`*/*`

**接口描述**:

**请求参数**:

**请求参数**:

| 参数名称 | 参数说明 | 请求类型 | 是否必须 | 数据类型       | schema |
| -------- | -------- | -------- | -------- | -------------- | ------ |
| id       |          | path     | true     | integer(int64) |        |

**响应状态**:

| 状态码 | 说明 | schema              |
| ------ | ---- | ------------------- |
| 200    | OK   | CommonResultBoolean |

**响应参数**:

| 参数名称 | 参数说明 | 类型           | schema         |
| -------- | -------- | -------------- | -------------- |
| code     |          | integer(int32) | integer(int32) |
| data     |          | boolean        |                |
| msg      |          | string         |                |

**响应示例**:

```javascript
{
	"code": 0,
	"data": true,
	"msg": ""
}
```

## 申请上架服务套餐

**接口地址**:`/publicbiz/employment/service-package/{id}/shelf`

**请求方式**:`PUT`

**请求数据类型**:`application/x-www-form-urlencoded`

**响应数据类型**:`*/*`

**接口描述**:

**请求参数**:

**请求参数**:

| 参数名称 | 参数说明 | 请求类型 | 是否必须 | 数据类型       | schema |
| -------- | -------- | -------- | -------- | -------------- | ------ |
| id       |          | path     | true     | integer(int64) |        |

**响应状态**:

| 状态码 | 说明 | schema              |
| ------ | ---- | ------------------- |
| 200    | OK   | CommonResultBoolean |

**响应参数**:

| 参数名称 | 参数说明 | 类型           | schema         |
| -------- | -------- | -------------- | -------------- |
| code     |          | integer(int32) | integer(int32) |
| data     |          | boolean        |                |
| msg      |          | string         |                |

**响应示例**:

```javascript
{
	"code": 0,
	"data": true,
	"msg": ""
}
```

## 审核服务套餐

**接口地址**:`/publicbiz/employment/service-package/{id}/audit`

**请求方式**:`PUT`

**请求数据类型**:`application/x-www-form-urlencoded,application/json`

**响应数据类型**:`*/*`

**接口描述**:

**请求示例**:

```javascript
{
  "auditStatus": "",
  "rejectReason": ""
}
```

**请求参数**:

**请求参数**:

| 参数名称 | 参数说明 | 请求类型 | 是否必须 | 数据类型 | schema |
| --- | --- | --- | --- | --- | --- |
| id |  | path | true | integer(int64) |  |
| servicePackageAuditStatusReqVO | 就业服务-服务套餐审核状态 Request VO | body | true | ServicePackageAuditStatusReqVO | ServicePackageAuditStatusReqVO |
| &emsp;&emsp;auditStatus | 审核状态 |  | true | string |  |
| &emsp;&emsp;rejectReason | 拒绝原因 |  | false | string |  |

**响应状态**:

| 状态码 | 说明 | schema              |
| ------ | ---- | ------------------- |
| 200    | OK   | CommonResultBoolean |

**响应参数**:

| 参数名称 | 参数说明 | 类型           | schema         |
| -------- | -------- | -------------- | -------------- |
| code     |          | integer(int32) | integer(int32) |
| data     |          | boolean        |                |
| msg      |          | string         |                |

**响应示例**:

```javascript
{
	"code": 0,
	"data": true,
	"msg": ""
}
```

## 撤回服务套餐

**接口地址**:`/publicbiz/employment/service-package/{id}/withdraw`

**请求方式**:`PUT`

**请求数据类型**:`application/x-www-form-urlencoded`

**响应数据类型**:`*/*`

**接口描述**:

**请求参数**:

**请求参数**:

| 参数名称 | 参数说明 | 请求类型 | 是否必须 | 数据类型       | schema |
| -------- | -------- | -------- | -------- | -------------- | ------ |
| id       |          | path     | true     | integer(int64) |        |

**响应状态**:

| 状态码 | 说明 | schema              |
| ------ | ---- | ------------------- |
| 200    | OK   | CommonResultBoolean |

**响应参数**:

| 参数名称 | 参数说明 | 类型           | schema         |
| -------- | -------- | -------------- | -------------- |
| code     |          | integer(int32) | integer(int32) |
| data     |          | boolean        |                |
| msg      |          | string         |                |

**响应示例**:

```javascript
{
	"code": 0,
	"data": true,
	"msg": ""
}
```
